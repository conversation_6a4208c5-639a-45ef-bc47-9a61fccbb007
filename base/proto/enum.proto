syntax = "proto3";

package proto;

option go_package = "./pb";

//After are enums.
// 矿洞格子类型
enum OreCeilType{
  BlockNone = 0; //空地
  BlockSingle = 1; //敲一下地块
  BlockDouble = 2; //敲两下地块
  BlockMonster = 3; //怪物地块
  BlockSpecial = 4; //特殊地块
  BlockNext = 5; //下一区域
  BlockNextSpecial = 6; //特殊区域
  BlockDoubleBreakage = 7; //敲两下地块破损状态
  BlockBack = 8; //从特殊区域返回
  BlockMonsterQunit = 9; //精英怪
  BlockStartPs = 10; //起点
  BlockItemBoom = 11; //炸弹
  BlockItemDrill = 12; //钻头
  BlockMonsterRunAway = 13; //逃跑地精的战斗
}

// 矿洞格子操作类型
enum OreCeilActionType{
  BreakNormal = 0; //普通镐子
  BreakNormalPlus = 1; //高级稿子
  BreakBomb = 2; //炸弹
  BreakDrill = 3; //钻头
  BreakBattle = 4; //挑战
  GotoNext = 5; //进入下一区域
  GotoSpecial = 6; //特殊区域
}

// 矿洞格子操作响应
enum OreCeilActionCode{
  OnSuccess = 0; //成功
  InvalidPosition = 1; //操作位置不正确
  InvalidActionType = 2; //选中格子不允许进行这个操作
  ItemNotEnough = 3; //道具不足
}

// 矿洞特殊区域类型
enum OreSpecialAreaType{
  MonsterQunit = 0; //精英怪
  OreArea = 1; //矿洞
}

// 日常挖矿特殊矿区
enum OrePageNextType{
  NormalNext = 0; //正常矿区
  GrayNext = 1; //(黑色)矿区
  BlueNext = 2; //蓝色矿区
}

// 钻头道具方向
enum OreBlockItemDrillDirection{
  Left = 0; //左
  Top = 1; //上
  Right = 2; //右
  Bottom = 3; //下
}

// 通缉令状态
enum ArrestState{
  NotCollected = 0; //未领取
  OnGoing = 1; //进行中
  DoneNoReward = 2; //完成未领奖
  Finished = 3; //完成已领奖
  Expired = 4; //过期
}

// 通缉令线索类型
enum ArrestClueType{
  PLACE = 0; //地点
  TIME = 1; //时间
}

// 通缉令地点类型
enum ArrestPlaceType{
  NONE = 0; //空
  TEXT = 1; //文案
  PIC = 2; //图片
}

// 通缉令时间类型
enum ArrestTimeType{
  ALL = 0; //全天
  DAY = 1; //白天
  NIGHT = 2; //黑夜
}

// 竞技场类型
enum PvpType{
  NORMAL = 0; //普通竞技场
  HIGH = 1; //高级竞技场
  PEAK = 2; //巅峰竞技场
}

// 广告类型
enum AdType{
  RecoveryTrainEnergy = 0; //恢复列车加速引擎能量
  RecoveryOreBreakItem = 1; //恢复矿洞镐子
}

// 记忆阁关卡状态
enum ProfileBranchLevelState{
  Locked = 0; //已锁定
  ReadyUnlock = 1; //可以解锁
  Unlocked = 2; //已解锁
  AnswerDone = 3; //已完成
}

// 护送数据状态
enum TransportDataState{
  NoneGet = 0; //未领取
  Pull = 1; //已经领取
  Done = 2; //已经完成
  Over = 3; //已经领奖
  Failed = 4; //失败
}

// 格子状态
enum FieldCeilState{
  Empty = 0; //没有任何作物
  NotWater = 1; //播种后未浇水
  Growing = 2; //作物成长中
  GrowDone = 3; //可以收获
}

// 车厢日常任务状态
enum CommonState{
  NotStart = 0; //未开始
  InProcess = 1; //进行中
  DoneWithoutReward = 2; //结束未领奖
  FinishWithReward = 3; //结束已领奖
}

// 车厢科技效果类型
enum TrainTechEffectType{
  ErrorEffect = 0; //无效果
  AddDeepExploreShipNum = 1; //增加深度探索飞船数量
  AddBedNum = 2; //对应寝室床位增加
  AddTrainSpeed = 3; //列车行驶速度增加,减少航行时间
  AddTrainDailyTaskNum = 4; //列车任务数量增加
  AddWorkPlaceNum = 5; //对应车厢工位增加
  AddDeepExploreSpeed = 6; //深度探索速度增加
  AddTimeMachineSpeed = 7; //时光机加速效果提升
  AddTrainLoad = 8; //车厢载重提升
  AddHeartOutputPerHour = 101; //每小时爱心产出增加
  AddHeartOutputTotal = 102; //爱心产出总量增加
  AddStardustOutputPerHour = 103; //每小时星尘产出增加
  AddStardustOutputTotal = 104; //星尘产出总量增加
  AddElectricityOutputPerHour = 105; //每小时电力产出增加
  AddElectricityOutputTotal = 106; //电力产出总量增加
  AddVitalityOutputPerHour = 107; //每小时元气值产出增加
  AddVitalityOutputTotal = 108; //元气值产出总量增加
  AddWaterOutputPerHour = 109; //每小时水资源产出增加
  AddWaterOutputTotal = 110; //水资源产出总量增加
}

// 时间宝石事件类型
enum TimeStoneEvent{
  TypeNone = 0; //未知
  TypeBlackHoleBattle = 1; //星海迷宫战斗
  TypeEquipMake = 2; //装备打造
  TypeJackpot = 3; //乘客邀请
}

// 订单状态
enum OrderState{
  NOT_PAY = 0; //未支付
  PAY = 1; //已支付未发放
  FINISH = 2; //已发放
  REFUND = 3; //已退款
  FAIL_TIMEOUT = 4; //失败-支付等待超时
}

//After are structs.
//After are messages.
