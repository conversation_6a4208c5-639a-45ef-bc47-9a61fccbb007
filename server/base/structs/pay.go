package structs

import (
	"train/common/pb"

	"github.com/samber/lo"
)

func NewPayModule() *PayModule {
	return &PayModule{
		NotFinishOrders: make([]*PayOrder, 0),
		PayCountMap:     make(map[string]int),
	}
}

type PayModule struct {
	NotFinishOrders []*PayOrder    `bson:"notFinishOrders"` // 未完成订单列表
	PayCountMap     map[string]int `bson:"payCountMap"`     // 订单购买次数
	plr             *Player        `bson:"-"`               // 玩家
}

func (p *PayModule) init(plr *Player) {
	p.plr = plr
}

// 是不是首购
func (p *PayModule) IsFirstPay(productIds string) bool {
	count := p.PayCountMap[productIds]
	return count == 0
}

func (p *PayModule) ToPb() *pb.Pay {
	return &pb.Pay{
		NotFinishOrders: lo.Map(p.NotFinishOrders, func(o *PayOrder, i int) *pb.NotFinishPayOrder { return o.ToPb() }),
		PayCountMap:     lo.MapEntries(p.PayCountMap, func(key string, val int) (string, int32) { return key, int32(val) }),
	}
}
