package structs

import "train/common/pb"

type PayOrder struct {
	OrderId      string        `bson:"orderId"`      // 内部订单号
	ThirdOrderId string        `bson:"thirdOrderId"` // 第三方订单号
	UserId       string        `bson:"userId"`       // 玩家id
	State        pb.OrderState `bson:"state"`        // 状态
	ProductId    string        `bson:"productId"`    // 商品id
	Platform     string        `bson:"platform"`     // 支付方式
	CreateTime   int           `bson:"createTime"`   // 创建时间
	PurchaseTime int           `bson:"purchaseTime"` // 支付时间
	FinishTime   int           `bson:"finishTime"`   // 完成时间
	CurrencyType string        `bson:"currencyType"` // 支付币种
	PayAmount    float64       `bson:"payAmount"`    // 支付金额
	Quantity     int           `bson:"quantity"`     // 数量
}
