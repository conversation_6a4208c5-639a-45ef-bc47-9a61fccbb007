// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.23.4
// source: enum.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// 矿洞格子类型
type OreCeilType int32

const (
	OreCeilType_BlockNone           OreCeilType = 0  //空地
	OreCeilType_BlockSingle         OreCeilType = 1  //敲一下地块
	OreCeilType_BlockDouble         OreCeilType = 2  //敲两下地块
	OreCeilType_BlockMonster        OreCeilType = 3  //怪物地块
	OreCeilType_BlockSpecial        OreCeilType = 4  //特殊地块
	OreCeilType_BlockNext           OreCeilType = 5  //下一区域
	OreCeilType_BlockNextSpecial    OreCeilType = 6  //特殊区域
	OreCeilType_BlockDoubleBreakage OreCeilType = 7  //敲两下地块破损状态
	OreCeilType_BlockBack           OreCeilType = 8  //从特殊区域返回
	OreCeilType_BlockMonsterQunit   OreCeilType = 9  //精英怪
	OreCeilType_BlockStartPs        OreCeilType = 10 //起点
	OreCeilType_BlockItemBoom       OreCeilType = 11 //炸弹
	OreCeilType_BlockItemDrill      OreCeilType = 12 //钻头
	OreCeilType_BlockMonsterRunAway OreCeilType = 13 //逃跑地精的战斗
)

// Enum value maps for OreCeilType.
var (
	OreCeilType_name = map[int32]string{
		0:  "BlockNone",
		1:  "BlockSingle",
		2:  "BlockDouble",
		3:  "BlockMonster",
		4:  "BlockSpecial",
		5:  "BlockNext",
		6:  "BlockNextSpecial",
		7:  "BlockDoubleBreakage",
		8:  "BlockBack",
		9:  "BlockMonsterQunit",
		10: "BlockStartPs",
		11: "BlockItemBoom",
		12: "BlockItemDrill",
		13: "BlockMonsterRunAway",
	}
	OreCeilType_value = map[string]int32{
		"BlockNone":           0,
		"BlockSingle":         1,
		"BlockDouble":         2,
		"BlockMonster":        3,
		"BlockSpecial":        4,
		"BlockNext":           5,
		"BlockNextSpecial":    6,
		"BlockDoubleBreakage": 7,
		"BlockBack":           8,
		"BlockMonsterQunit":   9,
		"BlockStartPs":        10,
		"BlockItemBoom":       11,
		"BlockItemDrill":      12,
		"BlockMonsterRunAway": 13,
	}
)

func (x OreCeilType) Enum() *OreCeilType {
	p := new(OreCeilType)
	*p = x
	return p
}

func (x OreCeilType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OreCeilType) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[0].Descriptor()
}

func (OreCeilType) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[0]
}

func (x OreCeilType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OreCeilType.Descriptor instead.
func (OreCeilType) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{0}
}

// 矿洞格子操作类型
type OreCeilActionType int32

const (
	OreCeilActionType_BreakNormal     OreCeilActionType = 0 //普通镐子
	OreCeilActionType_BreakNormalPlus OreCeilActionType = 1 //高级稿子
	OreCeilActionType_BreakBomb       OreCeilActionType = 2 //炸弹
	OreCeilActionType_BreakDrill      OreCeilActionType = 3 //钻头
	OreCeilActionType_BreakBattle     OreCeilActionType = 4 //挑战
	OreCeilActionType_GotoNext        OreCeilActionType = 5 //进入下一区域
	OreCeilActionType_GotoSpecial     OreCeilActionType = 6 //特殊区域
)

// Enum value maps for OreCeilActionType.
var (
	OreCeilActionType_name = map[int32]string{
		0: "BreakNormal",
		1: "BreakNormalPlus",
		2: "BreakBomb",
		3: "BreakDrill",
		4: "BreakBattle",
		5: "GotoNext",
		6: "GotoSpecial",
	}
	OreCeilActionType_value = map[string]int32{
		"BreakNormal":     0,
		"BreakNormalPlus": 1,
		"BreakBomb":       2,
		"BreakDrill":      3,
		"BreakBattle":     4,
		"GotoNext":        5,
		"GotoSpecial":     6,
	}
)

func (x OreCeilActionType) Enum() *OreCeilActionType {
	p := new(OreCeilActionType)
	*p = x
	return p
}

func (x OreCeilActionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OreCeilActionType) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[1].Descriptor()
}

func (OreCeilActionType) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[1]
}

func (x OreCeilActionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OreCeilActionType.Descriptor instead.
func (OreCeilActionType) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{1}
}

// 矿洞格子操作响应
type OreCeilActionCode int32

const (
	OreCeilActionCode_OnSuccess         OreCeilActionCode = 0 //成功
	OreCeilActionCode_InvalidPosition   OreCeilActionCode = 1 //操作位置不正确
	OreCeilActionCode_InvalidActionType OreCeilActionCode = 2 //选中格子不允许进行这个操作
	OreCeilActionCode_ItemNotEnough     OreCeilActionCode = 3 //道具不足
)

// Enum value maps for OreCeilActionCode.
var (
	OreCeilActionCode_name = map[int32]string{
		0: "OnSuccess",
		1: "InvalidPosition",
		2: "InvalidActionType",
		3: "ItemNotEnough",
	}
	OreCeilActionCode_value = map[string]int32{
		"OnSuccess":         0,
		"InvalidPosition":   1,
		"InvalidActionType": 2,
		"ItemNotEnough":     3,
	}
)

func (x OreCeilActionCode) Enum() *OreCeilActionCode {
	p := new(OreCeilActionCode)
	*p = x
	return p
}

func (x OreCeilActionCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OreCeilActionCode) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[2].Descriptor()
}

func (OreCeilActionCode) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[2]
}

func (x OreCeilActionCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OreCeilActionCode.Descriptor instead.
func (OreCeilActionCode) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{2}
}

// 矿洞特殊区域类型
type OreSpecialAreaType int32

const (
	OreSpecialAreaType_MonsterQunit OreSpecialAreaType = 0 //精英怪
	OreSpecialAreaType_OreArea      OreSpecialAreaType = 1 //矿洞
)

// Enum value maps for OreSpecialAreaType.
var (
	OreSpecialAreaType_name = map[int32]string{
		0: "MonsterQunit",
		1: "OreArea",
	}
	OreSpecialAreaType_value = map[string]int32{
		"MonsterQunit": 0,
		"OreArea":      1,
	}
)

func (x OreSpecialAreaType) Enum() *OreSpecialAreaType {
	p := new(OreSpecialAreaType)
	*p = x
	return p
}

func (x OreSpecialAreaType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OreSpecialAreaType) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[3].Descriptor()
}

func (OreSpecialAreaType) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[3]
}

func (x OreSpecialAreaType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OreSpecialAreaType.Descriptor instead.
func (OreSpecialAreaType) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{3}
}

// 日常挖矿特殊矿区
type OrePageNextType int32

const (
	OrePageNextType_NormalNext OrePageNextType = 0 //正常矿区
	OrePageNextType_GrayNext   OrePageNextType = 1 //(黑色)矿区
	OrePageNextType_BlueNext   OrePageNextType = 2 //蓝色矿区
)

// Enum value maps for OrePageNextType.
var (
	OrePageNextType_name = map[int32]string{
		0: "NormalNext",
		1: "GrayNext",
		2: "BlueNext",
	}
	OrePageNextType_value = map[string]int32{
		"NormalNext": 0,
		"GrayNext":   1,
		"BlueNext":   2,
	}
)

func (x OrePageNextType) Enum() *OrePageNextType {
	p := new(OrePageNextType)
	*p = x
	return p
}

func (x OrePageNextType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OrePageNextType) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[4].Descriptor()
}

func (OrePageNextType) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[4]
}

func (x OrePageNextType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OrePageNextType.Descriptor instead.
func (OrePageNextType) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{4}
}

// 钻头道具方向
type OreBlockItemDrillDirection int32

const (
	OreBlockItemDrillDirection_Left   OreBlockItemDrillDirection = 0 //左
	OreBlockItemDrillDirection_Top    OreBlockItemDrillDirection = 1 //上
	OreBlockItemDrillDirection_Right  OreBlockItemDrillDirection = 2 //右
	OreBlockItemDrillDirection_Bottom OreBlockItemDrillDirection = 3 //下
)

// Enum value maps for OreBlockItemDrillDirection.
var (
	OreBlockItemDrillDirection_name = map[int32]string{
		0: "Left",
		1: "Top",
		2: "Right",
		3: "Bottom",
	}
	OreBlockItemDrillDirection_value = map[string]int32{
		"Left":   0,
		"Top":    1,
		"Right":  2,
		"Bottom": 3,
	}
)

func (x OreBlockItemDrillDirection) Enum() *OreBlockItemDrillDirection {
	p := new(OreBlockItemDrillDirection)
	*p = x
	return p
}

func (x OreBlockItemDrillDirection) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OreBlockItemDrillDirection) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[5].Descriptor()
}

func (OreBlockItemDrillDirection) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[5]
}

func (x OreBlockItemDrillDirection) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OreBlockItemDrillDirection.Descriptor instead.
func (OreBlockItemDrillDirection) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{5}
}

// 通缉令状态
type ArrestState int32

const (
	ArrestState_NotCollected ArrestState = 0 //未领取
	ArrestState_OnGoing      ArrestState = 1 //进行中
	ArrestState_DoneNoReward ArrestState = 2 //完成未领奖
	ArrestState_Finished     ArrestState = 3 //完成已领奖
	ArrestState_Expired      ArrestState = 4 //过期
)

// Enum value maps for ArrestState.
var (
	ArrestState_name = map[int32]string{
		0: "NotCollected",
		1: "OnGoing",
		2: "DoneNoReward",
		3: "Finished",
		4: "Expired",
	}
	ArrestState_value = map[string]int32{
		"NotCollected": 0,
		"OnGoing":      1,
		"DoneNoReward": 2,
		"Finished":     3,
		"Expired":      4,
	}
)

func (x ArrestState) Enum() *ArrestState {
	p := new(ArrestState)
	*p = x
	return p
}

func (x ArrestState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ArrestState) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[6].Descriptor()
}

func (ArrestState) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[6]
}

func (x ArrestState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ArrestState.Descriptor instead.
func (ArrestState) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{6}
}

// 通缉令线索类型
type ArrestClueType int32

const (
	ArrestClueType_PLACE ArrestClueType = 0 //地点
	ArrestClueType_TIME  ArrestClueType = 1 //时间
)

// Enum value maps for ArrestClueType.
var (
	ArrestClueType_name = map[int32]string{
		0: "PLACE",
		1: "TIME",
	}
	ArrestClueType_value = map[string]int32{
		"PLACE": 0,
		"TIME":  1,
	}
)

func (x ArrestClueType) Enum() *ArrestClueType {
	p := new(ArrestClueType)
	*p = x
	return p
}

func (x ArrestClueType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ArrestClueType) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[7].Descriptor()
}

func (ArrestClueType) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[7]
}

func (x ArrestClueType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ArrestClueType.Descriptor instead.
func (ArrestClueType) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{7}
}

// 通缉令地点类型
type ArrestPlaceType int32

const (
	ArrestPlaceType_NONE ArrestPlaceType = 0 //空
	ArrestPlaceType_TEXT ArrestPlaceType = 1 //文案
	ArrestPlaceType_PIC  ArrestPlaceType = 2 //图片
)

// Enum value maps for ArrestPlaceType.
var (
	ArrestPlaceType_name = map[int32]string{
		0: "NONE",
		1: "TEXT",
		2: "PIC",
	}
	ArrestPlaceType_value = map[string]int32{
		"NONE": 0,
		"TEXT": 1,
		"PIC":  2,
	}
)

func (x ArrestPlaceType) Enum() *ArrestPlaceType {
	p := new(ArrestPlaceType)
	*p = x
	return p
}

func (x ArrestPlaceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ArrestPlaceType) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[8].Descriptor()
}

func (ArrestPlaceType) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[8]
}

func (x ArrestPlaceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ArrestPlaceType.Descriptor instead.
func (ArrestPlaceType) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{8}
}

// 通缉令时间类型
type ArrestTimeType int32

const (
	ArrestTimeType_ALL   ArrestTimeType = 0 //全天
	ArrestTimeType_DAY   ArrestTimeType = 1 //白天
	ArrestTimeType_NIGHT ArrestTimeType = 2 //黑夜
)

// Enum value maps for ArrestTimeType.
var (
	ArrestTimeType_name = map[int32]string{
		0: "ALL",
		1: "DAY",
		2: "NIGHT",
	}
	ArrestTimeType_value = map[string]int32{
		"ALL":   0,
		"DAY":   1,
		"NIGHT": 2,
	}
)

func (x ArrestTimeType) Enum() *ArrestTimeType {
	p := new(ArrestTimeType)
	*p = x
	return p
}

func (x ArrestTimeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ArrestTimeType) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[9].Descriptor()
}

func (ArrestTimeType) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[9]
}

func (x ArrestTimeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ArrestTimeType.Descriptor instead.
func (ArrestTimeType) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{9}
}

// 竞技场类型
type PvpType int32

const (
	PvpType_NORMAL PvpType = 0 //普通竞技场
	PvpType_HIGH   PvpType = 1 //高级竞技场
	PvpType_PEAK   PvpType = 2 //巅峰竞技场
)

// Enum value maps for PvpType.
var (
	PvpType_name = map[int32]string{
		0: "NORMAL",
		1: "HIGH",
		2: "PEAK",
	}
	PvpType_value = map[string]int32{
		"NORMAL": 0,
		"HIGH":   1,
		"PEAK":   2,
	}
)

func (x PvpType) Enum() *PvpType {
	p := new(PvpType)
	*p = x
	return p
}

func (x PvpType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PvpType) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[10].Descriptor()
}

func (PvpType) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[10]
}

func (x PvpType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PvpType.Descriptor instead.
func (PvpType) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{10}
}

// 广告类型
type AdType int32

const (
	AdType_RecoveryTrainEnergy  AdType = 0 //恢复列车加速引擎能量
	AdType_RecoveryOreBreakItem AdType = 1 //恢复矿洞镐子
)

// Enum value maps for AdType.
var (
	AdType_name = map[int32]string{
		0: "RecoveryTrainEnergy",
		1: "RecoveryOreBreakItem",
	}
	AdType_value = map[string]int32{
		"RecoveryTrainEnergy":  0,
		"RecoveryOreBreakItem": 1,
	}
)

func (x AdType) Enum() *AdType {
	p := new(AdType)
	*p = x
	return p
}

func (x AdType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AdType) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[11].Descriptor()
}

func (AdType) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[11]
}

func (x AdType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AdType.Descriptor instead.
func (AdType) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{11}
}

// 记忆阁关卡状态
type ProfileBranchLevelState int32

const (
	ProfileBranchLevelState_Locked      ProfileBranchLevelState = 0 //已锁定
	ProfileBranchLevelState_ReadyUnlock ProfileBranchLevelState = 1 //可以解锁
	ProfileBranchLevelState_Unlocked    ProfileBranchLevelState = 2 //已解锁
	ProfileBranchLevelState_AnswerDone  ProfileBranchLevelState = 3 //已完成
)

// Enum value maps for ProfileBranchLevelState.
var (
	ProfileBranchLevelState_name = map[int32]string{
		0: "Locked",
		1: "ReadyUnlock",
		2: "Unlocked",
		3: "AnswerDone",
	}
	ProfileBranchLevelState_value = map[string]int32{
		"Locked":      0,
		"ReadyUnlock": 1,
		"Unlocked":    2,
		"AnswerDone":  3,
	}
)

func (x ProfileBranchLevelState) Enum() *ProfileBranchLevelState {
	p := new(ProfileBranchLevelState)
	*p = x
	return p
}

func (x ProfileBranchLevelState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProfileBranchLevelState) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[12].Descriptor()
}

func (ProfileBranchLevelState) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[12]
}

func (x ProfileBranchLevelState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProfileBranchLevelState.Descriptor instead.
func (ProfileBranchLevelState) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{12}
}

// 护送数据状态
type TransportDataState int32

const (
	TransportDataState_NoneGet TransportDataState = 0 //未领取
	TransportDataState_Pull    TransportDataState = 1 //已经领取
	TransportDataState_Done    TransportDataState = 2 //已经完成
	TransportDataState_Over    TransportDataState = 3 //已经领奖
	TransportDataState_Failed  TransportDataState = 4 //失败
)

// Enum value maps for TransportDataState.
var (
	TransportDataState_name = map[int32]string{
		0: "NoneGet",
		1: "Pull",
		2: "Done",
		3: "Over",
		4: "Failed",
	}
	TransportDataState_value = map[string]int32{
		"NoneGet": 0,
		"Pull":    1,
		"Done":    2,
		"Over":    3,
		"Failed":  4,
	}
)

func (x TransportDataState) Enum() *TransportDataState {
	p := new(TransportDataState)
	*p = x
	return p
}

func (x TransportDataState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TransportDataState) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[13].Descriptor()
}

func (TransportDataState) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[13]
}

func (x TransportDataState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TransportDataState.Descriptor instead.
func (TransportDataState) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{13}
}

// 格子状态
type FieldCeilState int32

const (
	FieldCeilState_Empty    FieldCeilState = 0 //没有任何作物
	FieldCeilState_NotWater FieldCeilState = 1 //播种后未浇水
	FieldCeilState_Growing  FieldCeilState = 2 //作物成长中
	FieldCeilState_GrowDone FieldCeilState = 3 //可以收获
)

// Enum value maps for FieldCeilState.
var (
	FieldCeilState_name = map[int32]string{
		0: "Empty",
		1: "NotWater",
		2: "Growing",
		3: "GrowDone",
	}
	FieldCeilState_value = map[string]int32{
		"Empty":    0,
		"NotWater": 1,
		"Growing":  2,
		"GrowDone": 3,
	}
)

func (x FieldCeilState) Enum() *FieldCeilState {
	p := new(FieldCeilState)
	*p = x
	return p
}

func (x FieldCeilState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FieldCeilState) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[14].Descriptor()
}

func (FieldCeilState) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[14]
}

func (x FieldCeilState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FieldCeilState.Descriptor instead.
func (FieldCeilState) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{14}
}

// 车厢日常任务状态
type CommonState int32

const (
	CommonState_NotStart          CommonState = 0 //未开始
	CommonState_InProcess         CommonState = 1 //进行中
	CommonState_DoneWithoutReward CommonState = 2 //结束未领奖
	CommonState_FinishWithReward  CommonState = 3 //结束已领奖
)

// Enum value maps for CommonState.
var (
	CommonState_name = map[int32]string{
		0: "NotStart",
		1: "InProcess",
		2: "DoneWithoutReward",
		3: "FinishWithReward",
	}
	CommonState_value = map[string]int32{
		"NotStart":          0,
		"InProcess":         1,
		"DoneWithoutReward": 2,
		"FinishWithReward":  3,
	}
)

func (x CommonState) Enum() *CommonState {
	p := new(CommonState)
	*p = x
	return p
}

func (x CommonState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CommonState) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[15].Descriptor()
}

func (CommonState) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[15]
}

func (x CommonState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CommonState.Descriptor instead.
func (CommonState) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{15}
}

// 车厢科技效果类型
type TrainTechEffectType int32

const (
	TrainTechEffectType_ErrorEffect                 TrainTechEffectType = 0   //无效果
	TrainTechEffectType_AddDeepExploreShipNum       TrainTechEffectType = 1   //增加深度探索飞船数量
	TrainTechEffectType_AddBedNum                   TrainTechEffectType = 2   //对应寝室床位增加
	TrainTechEffectType_AddTrainSpeed               TrainTechEffectType = 3   //列车行驶速度增加,减少航行时间
	TrainTechEffectType_AddTrainDailyTaskNum        TrainTechEffectType = 4   //列车任务数量增加
	TrainTechEffectType_AddWorkPlaceNum             TrainTechEffectType = 5   //对应车厢工位增加
	TrainTechEffectType_AddDeepExploreSpeed         TrainTechEffectType = 6   //深度探索速度增加
	TrainTechEffectType_AddTimeMachineSpeed         TrainTechEffectType = 7   //时光机加速效果提升
	TrainTechEffectType_AddTrainLoad                TrainTechEffectType = 8   //车厢载重提升
	TrainTechEffectType_AddHeartOutputPerHour       TrainTechEffectType = 101 //每小时爱心产出增加
	TrainTechEffectType_AddHeartOutputTotal         TrainTechEffectType = 102 //爱心产出总量增加
	TrainTechEffectType_AddStardustOutputPerHour    TrainTechEffectType = 103 //每小时星尘产出增加
	TrainTechEffectType_AddStardustOutputTotal      TrainTechEffectType = 104 //星尘产出总量增加
	TrainTechEffectType_AddElectricityOutputPerHour TrainTechEffectType = 105 //每小时电力产出增加
	TrainTechEffectType_AddElectricityOutputTotal   TrainTechEffectType = 106 //电力产出总量增加
	TrainTechEffectType_AddVitalityOutputPerHour    TrainTechEffectType = 107 //每小时元气值产出增加
	TrainTechEffectType_AddVitalityOutputTotal      TrainTechEffectType = 108 //元气值产出总量增加
	TrainTechEffectType_AddWaterOutputPerHour       TrainTechEffectType = 109 //每小时水资源产出增加
	TrainTechEffectType_AddWaterOutputTotal         TrainTechEffectType = 110 //水资源产出总量增加
)

// Enum value maps for TrainTechEffectType.
var (
	TrainTechEffectType_name = map[int32]string{
		0:   "ErrorEffect",
		1:   "AddDeepExploreShipNum",
		2:   "AddBedNum",
		3:   "AddTrainSpeed",
		4:   "AddTrainDailyTaskNum",
		5:   "AddWorkPlaceNum",
		6:   "AddDeepExploreSpeed",
		7:   "AddTimeMachineSpeed",
		8:   "AddTrainLoad",
		101: "AddHeartOutputPerHour",
		102: "AddHeartOutputTotal",
		103: "AddStardustOutputPerHour",
		104: "AddStardustOutputTotal",
		105: "AddElectricityOutputPerHour",
		106: "AddElectricityOutputTotal",
		107: "AddVitalityOutputPerHour",
		108: "AddVitalityOutputTotal",
		109: "AddWaterOutputPerHour",
		110: "AddWaterOutputTotal",
	}
	TrainTechEffectType_value = map[string]int32{
		"ErrorEffect":                 0,
		"AddDeepExploreShipNum":       1,
		"AddBedNum":                   2,
		"AddTrainSpeed":               3,
		"AddTrainDailyTaskNum":        4,
		"AddWorkPlaceNum":             5,
		"AddDeepExploreSpeed":         6,
		"AddTimeMachineSpeed":         7,
		"AddTrainLoad":                8,
		"AddHeartOutputPerHour":       101,
		"AddHeartOutputTotal":         102,
		"AddStardustOutputPerHour":    103,
		"AddStardustOutputTotal":      104,
		"AddElectricityOutputPerHour": 105,
		"AddElectricityOutputTotal":   106,
		"AddVitalityOutputPerHour":    107,
		"AddVitalityOutputTotal":      108,
		"AddWaterOutputPerHour":       109,
		"AddWaterOutputTotal":         110,
	}
)

func (x TrainTechEffectType) Enum() *TrainTechEffectType {
	p := new(TrainTechEffectType)
	*p = x
	return p
}

func (x TrainTechEffectType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TrainTechEffectType) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[16].Descriptor()
}

func (TrainTechEffectType) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[16]
}

func (x TrainTechEffectType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TrainTechEffectType.Descriptor instead.
func (TrainTechEffectType) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{16}
}

// 时间宝石事件类型
type TimeStoneEvent int32

const (
	TimeStoneEvent_TypeNone            TimeStoneEvent = 0 //未知
	TimeStoneEvent_TypeBlackHoleBattle TimeStoneEvent = 1 //星海迷宫战斗
	TimeStoneEvent_TypeEquipMake       TimeStoneEvent = 2 //装备打造
	TimeStoneEvent_TypeJackpot         TimeStoneEvent = 3 //乘客邀请
)

// Enum value maps for TimeStoneEvent.
var (
	TimeStoneEvent_name = map[int32]string{
		0: "TypeNone",
		1: "TypeBlackHoleBattle",
		2: "TypeEquipMake",
		3: "TypeJackpot",
	}
	TimeStoneEvent_value = map[string]int32{
		"TypeNone":            0,
		"TypeBlackHoleBattle": 1,
		"TypeEquipMake":       2,
		"TypeJackpot":         3,
	}
)

func (x TimeStoneEvent) Enum() *TimeStoneEvent {
	p := new(TimeStoneEvent)
	*p = x
	return p
}

func (x TimeStoneEvent) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TimeStoneEvent) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[17].Descriptor()
}

func (TimeStoneEvent) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[17]
}

func (x TimeStoneEvent) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TimeStoneEvent.Descriptor instead.
func (TimeStoneEvent) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{17}
}

// 订单状态
type OrderState int32

const (
	OrderState_NOT_PAY OrderState = 0 //未支付
	OrderState_PAY     OrderState = 1 //已支付未发放
	OrderState_FINISH  OrderState = 2 //已发放
	OrderState_REFUND  OrderState = 3 //已退款
)

// Enum value maps for OrderState.
var (
	OrderState_name = map[int32]string{
		0: "NOT_PAY",
		1: "PAY",
		2: "FINISH",
		3: "REFUND",
	}
	OrderState_value = map[string]int32{
		"NOT_PAY": 0,
		"PAY":     1,
		"FINISH":  2,
		"REFUND":  3,
	}
)

func (x OrderState) Enum() *OrderState {
	p := new(OrderState)
	*p = x
	return p
}

func (x OrderState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OrderState) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[18].Descriptor()
}

func (OrderState) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[18]
}

func (x OrderState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OrderState.Descriptor instead.
func (OrderState) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{18}
}

var File_enum_proto protoreflect.FileDescriptor

var file_enum_proto_rawDesc = []byte{
	0x0a, 0x0a, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2a, 0x98, 0x02, 0x0a, 0x0b, 0x4f, 0x72, 0x65, 0x43, 0x65, 0x69, 0x6c, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x0d, 0x0a, 0x09, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x4e, 0x6f, 0x6e, 0x65,
	0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x53, 0x69, 0x6e, 0x67, 0x6c,
	0x65, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x6f, 0x75, 0x62,
	0x6c, 0x65, 0x10, 0x02, 0x12, 0x10, 0x0a, 0x0c, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x4d, 0x6f, 0x6e,
	0x73, 0x74, 0x65, 0x72, 0x10, 0x03, 0x12, 0x10, 0x0a, 0x0c, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x53,
	0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x10, 0x04, 0x12, 0x0d, 0x0a, 0x09, 0x42, 0x6c, 0x6f, 0x63,
	0x6b, 0x4e, 0x65, 0x78, 0x74, 0x10, 0x05, 0x12, 0x14, 0x0a, 0x10, 0x42, 0x6c, 0x6f, 0x63, 0x6b,
	0x4e, 0x65, 0x78, 0x74, 0x53, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x10, 0x06, 0x12, 0x17, 0x0a,
	0x13, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x42, 0x72, 0x65, 0x61,
	0x6b, 0x61, 0x67, 0x65, 0x10, 0x07, 0x12, 0x0d, 0x0a, 0x09, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x42,
	0x61, 0x63, 0x6b, 0x10, 0x08, 0x12, 0x15, 0x0a, 0x11, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x4d, 0x6f,
	0x6e, 0x73, 0x74, 0x65, 0x72, 0x51, 0x75, 0x6e, 0x69, 0x74, 0x10, 0x09, 0x12, 0x10, 0x0a, 0x0c,
	0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x72, 0x74, 0x50, 0x73, 0x10, 0x0a, 0x12, 0x11,
	0x0a, 0x0d, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x6f, 0x6f, 0x6d, 0x10,
	0x0b, 0x12, 0x12, 0x0a, 0x0e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x74, 0x65, 0x6d, 0x44, 0x72,
	0x69, 0x6c, 0x6c, 0x10, 0x0c, 0x12, 0x17, 0x0a, 0x13, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x4d, 0x6f,
	0x6e, 0x73, 0x74, 0x65, 0x72, 0x52, 0x75, 0x6e, 0x41, 0x77, 0x61, 0x79, 0x10, 0x0d, 0x2a, 0x88,
	0x01, 0x0a, 0x11, 0x4f, 0x72, 0x65, 0x43, 0x65, 0x69, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x0f, 0x0a, 0x0b, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x4e, 0x6f, 0x72,
	0x6d, 0x61, 0x6c, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x4e, 0x6f,
	0x72, 0x6d, 0x61, 0x6c, 0x50, 0x6c, 0x75, 0x73, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x42, 0x72,
	0x65, 0x61, 0x6b, 0x42, 0x6f, 0x6d, 0x62, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x42, 0x72, 0x65,
	0x61, 0x6b, 0x44, 0x72, 0x69, 0x6c, 0x6c, 0x10, 0x03, 0x12, 0x0f, 0x0a, 0x0b, 0x42, 0x72, 0x65,
	0x61, 0x6b, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08, 0x47, 0x6f,
	0x74, 0x6f, 0x4e, 0x65, 0x78, 0x74, 0x10, 0x05, 0x12, 0x0f, 0x0a, 0x0b, 0x47, 0x6f, 0x74, 0x6f,
	0x53, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x10, 0x06, 0x2a, 0x61, 0x0a, 0x11, 0x4f, 0x72, 0x65,
	0x43, 0x65, 0x69, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x0d,
	0x0a, 0x09, 0x4f, 0x6e, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x10, 0x00, 0x12, 0x13, 0x0a,
	0x0f, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d, 0x49, 0x74, 0x65,
	0x6d, 0x4e, 0x6f, 0x74, 0x45, 0x6e, 0x6f, 0x75, 0x67, 0x68, 0x10, 0x03, 0x2a, 0x33, 0x0a, 0x12,
	0x4f, 0x72, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x41, 0x72, 0x65, 0x61, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x10, 0x0a, 0x0c, 0x4d, 0x6f, 0x6e, 0x73, 0x74, 0x65, 0x72, 0x51, 0x75, 0x6e,
	0x69, 0x74, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x4f, 0x72, 0x65, 0x41, 0x72, 0x65, 0x61, 0x10,
	0x01, 0x2a, 0x3d, 0x0a, 0x0f, 0x4f, 0x72, 0x65, 0x50, 0x61, 0x67, 0x65, 0x4e, 0x65, 0x78, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x0a, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x4e, 0x65,
	0x78, 0x74, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x47, 0x72, 0x61, 0x79, 0x4e, 0x65, 0x78, 0x74,
	0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x42, 0x6c, 0x75, 0x65, 0x4e, 0x65, 0x78, 0x74, 0x10, 0x02,
	0x2a, 0x46, 0x0a, 0x1a, 0x4f, 0x72, 0x65, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x74, 0x65, 0x6d,
	0x44, 0x72, 0x69, 0x6c, 0x6c, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x08,
	0x0a, 0x04, 0x4c, 0x65, 0x66, 0x74, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x54, 0x6f, 0x70, 0x10,
	0x01, 0x12, 0x09, 0x0a, 0x05, 0x52, 0x69, 0x67, 0x68, 0x74, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06,
	0x42, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x10, 0x03, 0x2a, 0x59, 0x0a, 0x0b, 0x41, 0x72, 0x72, 0x65,
	0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x10, 0x0a, 0x0c, 0x4e, 0x6f, 0x74, 0x43, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x4f, 0x6e, 0x47,
	0x6f, 0x69, 0x6e, 0x67, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x44, 0x6f, 0x6e, 0x65, 0x4e, 0x6f,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x46, 0x69, 0x6e, 0x69,
	0x73, 0x68, 0x65, 0x64, 0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65,
	0x64, 0x10, 0x04, 0x2a, 0x25, 0x0a, 0x0e, 0x41, 0x72, 0x72, 0x65, 0x73, 0x74, 0x43, 0x6c, 0x75,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x09, 0x0a, 0x05, 0x50, 0x4c, 0x41, 0x43, 0x45, 0x10, 0x00,
	0x12, 0x08, 0x0a, 0x04, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x01, 0x2a, 0x2e, 0x0a, 0x0f, 0x41, 0x72,
	0x72, 0x65, 0x73, 0x74, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x08, 0x0a,
	0x04, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x54, 0x45, 0x58, 0x54, 0x10,
	0x01, 0x12, 0x07, 0x0a, 0x03, 0x50, 0x49, 0x43, 0x10, 0x02, 0x2a, 0x2d, 0x0a, 0x0e, 0x41, 0x72,
	0x72, 0x65, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x07, 0x0a, 0x03,
	0x41, 0x4c, 0x4c, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x44, 0x41, 0x59, 0x10, 0x01, 0x12, 0x09,
	0x0a, 0x05, 0x4e, 0x49, 0x47, 0x48, 0x54, 0x10, 0x02, 0x2a, 0x29, 0x0a, 0x07, 0x50, 0x76, 0x70,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x0a, 0x0a, 0x06, 0x4e, 0x4f, 0x52, 0x4d, 0x41, 0x4c, 0x10, 0x00,
	0x12, 0x08, 0x0a, 0x04, 0x48, 0x49, 0x47, 0x48, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x50, 0x45,
	0x41, 0x4b, 0x10, 0x02, 0x2a, 0x3b, 0x0a, 0x06, 0x41, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17,
	0x0a, 0x13, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x45,
	0x6e, 0x65, 0x72, 0x67, 0x79, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x52, 0x65, 0x63, 0x6f, 0x76,
	0x65, 0x72, 0x79, 0x4f, 0x72, 0x65, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x49, 0x74, 0x65, 0x6d, 0x10,
	0x01, 0x2a, 0x54, 0x0a, 0x17, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x42, 0x72, 0x61, 0x6e,
	0x63, 0x68, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x0a, 0x0a, 0x06,
	0x4c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x52, 0x65, 0x61, 0x64,
	0x79, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x55, 0x6e, 0x6c,
	0x6f, 0x63, 0x6b, 0x65, 0x64, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x41, 0x6e, 0x73, 0x77, 0x65,
	0x72, 0x44, 0x6f, 0x6e, 0x65, 0x10, 0x03, 0x2a, 0x4b, 0x0a, 0x12, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x0b, 0x0a,
	0x07, 0x4e, 0x6f, 0x6e, 0x65, 0x47, 0x65, 0x74, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x50, 0x75,
	0x6c, 0x6c, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x44, 0x6f, 0x6e, 0x65, 0x10, 0x02, 0x12, 0x08,
	0x0a, 0x04, 0x4f, 0x76, 0x65, 0x72, 0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x61, 0x69, 0x6c,
	0x65, 0x64, 0x10, 0x04, 0x2a, 0x44, 0x0a, 0x0e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x43, 0x65, 0x69,
	0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x10,
	0x00, 0x12, 0x0c, 0x0a, 0x08, 0x4e, 0x6f, 0x74, 0x57, 0x61, 0x74, 0x65, 0x72, 0x10, 0x01, 0x12,
	0x0b, 0x0a, 0x07, 0x47, 0x72, 0x6f, 0x77, 0x69, 0x6e, 0x67, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08,
	0x47, 0x72, 0x6f, 0x77, 0x44, 0x6f, 0x6e, 0x65, 0x10, 0x03, 0x2a, 0x57, 0x0a, 0x0b, 0x43, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x0c, 0x0a, 0x08, 0x4e, 0x6f, 0x74,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x49, 0x6e, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x44, 0x6f, 0x6e, 0x65, 0x57, 0x69,
	0x74, 0x68, 0x6f, 0x75, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x10, 0x02, 0x12, 0x14, 0x0a,
	0x10, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x57, 0x69, 0x74, 0x68, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x10, 0x03, 0x2a, 0xf2, 0x03, 0x0a, 0x13, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x54, 0x65, 0x63,
	0x68, 0x45, 0x66, 0x66, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0f, 0x0a, 0x0b, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x45, 0x66, 0x66, 0x65, 0x63, 0x74, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15,
	0x41, 0x64, 0x64, 0x44, 0x65, 0x65, 0x70, 0x45, 0x78, 0x70, 0x6c, 0x6f, 0x72, 0x65, 0x53, 0x68,
	0x69, 0x70, 0x4e, 0x75, 0x6d, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x41, 0x64, 0x64, 0x42, 0x65,
	0x64, 0x4e, 0x75, 0x6d, 0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d, 0x41, 0x64, 0x64, 0x54, 0x72, 0x61,
	0x69, 0x6e, 0x53, 0x70, 0x65, 0x65, 0x64, 0x10, 0x03, 0x12, 0x18, 0x0a, 0x14, 0x41, 0x64, 0x64,
	0x54, 0x72, 0x61, 0x69, 0x6e, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x4e, 0x75,
	0x6d, 0x10, 0x04, 0x12, 0x13, 0x0a, 0x0f, 0x41, 0x64, 0x64, 0x57, 0x6f, 0x72, 0x6b, 0x50, 0x6c,
	0x61, 0x63, 0x65, 0x4e, 0x75, 0x6d, 0x10, 0x05, 0x12, 0x17, 0x0a, 0x13, 0x41, 0x64, 0x64, 0x44,
	0x65, 0x65, 0x70, 0x45, 0x78, 0x70, 0x6c, 0x6f, 0x72, 0x65, 0x53, 0x70, 0x65, 0x65, 0x64, 0x10,
	0x06, 0x12, 0x17, 0x0a, 0x13, 0x41, 0x64, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x61, 0x63, 0x68,
	0x69, 0x6e, 0x65, 0x53, 0x70, 0x65, 0x65, 0x64, 0x10, 0x07, 0x12, 0x10, 0x0a, 0x0c, 0x41, 0x64,
	0x64, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x4c, 0x6f, 0x61, 0x64, 0x10, 0x08, 0x12, 0x19, 0x0a, 0x15,
	0x41, 0x64, 0x64, 0x48, 0x65, 0x61, 0x72, 0x74, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x50, 0x65,
	0x72, 0x48, 0x6f, 0x75, 0x72, 0x10, 0x65, 0x12, 0x17, 0x0a, 0x13, 0x41, 0x64, 0x64, 0x48, 0x65,
	0x61, 0x72, 0x74, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x10, 0x66,
	0x12, 0x1c, 0x0a, 0x18, 0x41, 0x64, 0x64, 0x53, 0x74, 0x61, 0x72, 0x64, 0x75, 0x73, 0x74, 0x4f,
	0x75, 0x74, 0x70, 0x75, 0x74, 0x50, 0x65, 0x72, 0x48, 0x6f, 0x75, 0x72, 0x10, 0x67, 0x12, 0x1a,
	0x0a, 0x16, 0x41, 0x64, 0x64, 0x53, 0x74, 0x61, 0x72, 0x64, 0x75, 0x73, 0x74, 0x4f, 0x75, 0x74,
	0x70, 0x75, 0x74, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x10, 0x68, 0x12, 0x1f, 0x0a, 0x1b, 0x41, 0x64,
	0x64, 0x45, 0x6c, 0x65, 0x63, 0x74, 0x72, 0x69, 0x63, 0x69, 0x74, 0x79, 0x4f, 0x75, 0x74, 0x70,
	0x75, 0x74, 0x50, 0x65, 0x72, 0x48, 0x6f, 0x75, 0x72, 0x10, 0x69, 0x12, 0x1d, 0x0a, 0x19, 0x41,
	0x64, 0x64, 0x45, 0x6c, 0x65, 0x63, 0x74, 0x72, 0x69, 0x63, 0x69, 0x74, 0x79, 0x4f, 0x75, 0x74,
	0x70, 0x75, 0x74, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x10, 0x6a, 0x12, 0x1c, 0x0a, 0x18, 0x41, 0x64,
	0x64, 0x56, 0x69, 0x74, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x50,
	0x65, 0x72, 0x48, 0x6f, 0x75, 0x72, 0x10, 0x6b, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x64, 0x64, 0x56,
	0x69, 0x74, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x54, 0x6f, 0x74,
	0x61, 0x6c, 0x10, 0x6c, 0x12, 0x19, 0x0a, 0x15, 0x41, 0x64, 0x64, 0x57, 0x61, 0x74, 0x65, 0x72,
	0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x50, 0x65, 0x72, 0x48, 0x6f, 0x75, 0x72, 0x10, 0x6d, 0x12,
	0x17, 0x0a, 0x13, 0x41, 0x64, 0x64, 0x57, 0x61, 0x74, 0x65, 0x72, 0x4f, 0x75, 0x74, 0x70, 0x75,
	0x74, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x10, 0x6e, 0x2a, 0x5b, 0x0a, 0x0e, 0x54, 0x69, 0x6d, 0x65,
	0x53, 0x74, 0x6f, 0x6e, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x0c, 0x0a, 0x08, 0x54, 0x79,
	0x70, 0x65, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x54, 0x79, 0x70, 0x65,
	0x42, 0x6c, 0x61, 0x63, 0x6b, 0x48, 0x6f, 0x6c, 0x65, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x10,
	0x01, 0x12, 0x11, 0x0a, 0x0d, 0x54, 0x79, 0x70, 0x65, 0x45, 0x71, 0x75, 0x69, 0x70, 0x4d, 0x61,
	0x6b, 0x65, 0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x79, 0x70, 0x65, 0x4a, 0x61, 0x63, 0x6b,
	0x70, 0x6f, 0x74, 0x10, 0x03, 0x2a, 0x3a, 0x0a, 0x0a, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x4e, 0x4f, 0x54, 0x5f, 0x50, 0x41, 0x59, 0x10, 0x00,
	0x12, 0x07, 0x0a, 0x03, 0x50, 0x41, 0x59, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x49, 0x4e,
	0x49, 0x53, 0x48, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x10,
	0x03, 0x42, 0x06, 0x5a, 0x04, 0x2e, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_enum_proto_rawDescOnce sync.Once
	file_enum_proto_rawDescData = file_enum_proto_rawDesc
)

func file_enum_proto_rawDescGZIP() []byte {
	file_enum_proto_rawDescOnce.Do(func() {
		file_enum_proto_rawDescData = protoimpl.X.CompressGZIP(file_enum_proto_rawDescData)
	})
	return file_enum_proto_rawDescData
}

var file_enum_proto_enumTypes = make([]protoimpl.EnumInfo, 19)
var file_enum_proto_goTypes = []interface{}{
	(OreCeilType)(0),                // 0: proto.OreCeilType
	(OreCeilActionType)(0),          // 1: proto.OreCeilActionType
	(OreCeilActionCode)(0),          // 2: proto.OreCeilActionCode
	(OreSpecialAreaType)(0),         // 3: proto.OreSpecialAreaType
	(OrePageNextType)(0),            // 4: proto.OrePageNextType
	(OreBlockItemDrillDirection)(0), // 5: proto.OreBlockItemDrillDirection
	(ArrestState)(0),                // 6: proto.ArrestState
	(ArrestClueType)(0),             // 7: proto.ArrestClueType
	(ArrestPlaceType)(0),            // 8: proto.ArrestPlaceType
	(ArrestTimeType)(0),             // 9: proto.ArrestTimeType
	(PvpType)(0),                    // 10: proto.PvpType
	(AdType)(0),                     // 11: proto.AdType
	(ProfileBranchLevelState)(0),    // 12: proto.ProfileBranchLevelState
	(TransportDataState)(0),         // 13: proto.TransportDataState
	(FieldCeilState)(0),             // 14: proto.FieldCeilState
	(CommonState)(0),                // 15: proto.CommonState
	(TrainTechEffectType)(0),        // 16: proto.TrainTechEffectType
	(TimeStoneEvent)(0),             // 17: proto.TimeStoneEvent
	(OrderState)(0),                 // 18: proto.OrderState
}
var file_enum_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_enum_proto_init() }
func file_enum_proto_init() {
	if File_enum_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_enum_proto_rawDesc,
			NumEnums:      19,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_enum_proto_goTypes,
		DependencyIndexes: file_enum_proto_depIdxs,
		EnumInfos:         file_enum_proto_enumTypes,
	}.Build()
	File_enum_proto = out.File
	file_enum_proto_rawDesc = nil
	file_enum_proto_goTypes = nil
	file_enum_proto_depIdxs = nil
}
