package main

import (
	"context"
	"flag"
	"fmt"
	"math/rand"
	_ "net/http/pprof"
	"os"
	"strconv"
	"time"
	"train/base/cfg"
	"train/base/enum"
	"train/base/manager"
	"train/base/script"
	com "train/common"
	comm "train/common"
	"train/common/pb"
	"train/common/ta"
	"train/db"
	"train/db/lua"
	"train/game"
	"train/game/cmd"
	mgate "train/gate"
	"train/http"
	"train/login"
	"train/task"
	ut "train/utils"
	wechatpay "train/utils/wechat_pay"

	"github.com/bamzi/jobrunner"
	"github.com/hashicorp/consul/api"
	mqant "github.com/huyangv/vmqant"
	"github.com/huyangv/vmqant/log"
	"github.com/huyangv/vmqant/module"
	"github.com/huyangv/vmqant/registry"
	"github.com/huyangv/vmqant/registry/consul"
	"github.com/huyangv/vmqant/selector"
	"github.com/nats-io/nats.go"
	"github.com/sasha-s/go-deadlock"
)

var App module.App

func init() {
	flag.StringVar(&script.ServerType, "type", "dev", "当前进程需要启动的服务器类型,需要和配置文件的ProcessID匹配才能被启动（loin|game|gate|http|cross|dev）.")
	flag.IntVar(&script.Sid, "sid", 1, "如果type的类型是game,需要sid来启动对应的区服.")
}

func main() {
	script.CallBeforeApplicationLaunchedSync()

	rs := consul.NewRegistry(func(options *registry.Options) {
		options.Addrs = []string{com.GetConsulUrl()}
	})

	// nats 认证
	token := com.GetConsulToken()
	natsOpt := make([]nats.Option, 0)
	natsOpt = append(natsOpt, nats.MaxReconnects(10000))
	if token != "" {
		natsOpt = append(natsOpt, nats.Token(token))
	}
	nc, err := nats.Connect(com.GetNatsUrl(), natsOpt...)
	if err != nil {
		log.Error("nats error %v", err)
		return
	}

	app := mqant.CreateApp(
		module.ProcessID(script.ServerType),
		module.Debug(com.IsDebug()),            //只有是在调试模式下才会在控制台打印日志, 非调试模式下只在日志文件中输出日志
		module.Nats(nc),                        //指定nats rpc
		module.Registry(rs),                    //指定服务发现
		module.RegisterTTL(10*time.Second),     //TTL指定在发现之后注册的信息存在多长时间 然后过期并被删除
		module.RegisterInterval(5*time.Second), //时间间隔是服务应该重新注册的时间，以保留其在服务发现中的注册信息
	)
	App = app
	comm.GetApp = func() module.App {
		return app
	}
	comm.GetConsulClient = func() *api.Client {
		if c := rs.GetConsulClient(); c != nil {
			return c
		}
		log.Error("GetConsulClient Error, client is nil.")
		return nil
	}
	//重写返回客户端时的协议
	app.SetProtocolMarshal(func(Trace string, Result interface{}, Error string) (module.ProtocolMarshal, string) {
		// 假设返回客户端的必然是protobuf 此处直接断言处理
		data, _ := Result.([]byte)
		if Error != "" {
			log.Error("Trace:%s,返回客户端消息时出错:%s", Trace, Error)
			data = pb.ProtoMarshalForce(&pb.S2C_ErrorResultMessage{
				Code: -500,
			})
		}
		return app.NewProtocolMarshal(data), ""
	})

	// 配置解析完成
	_ = app.OnConfigurationLoaded(func(app module.App) {
		script.SetLogger()
		script.LaunchAllOrNotAtDev(app)
		// 非gate服启动逻辑
		if script.Mark&enum.MarkGame > 0 || script.Mark&enum.MarkLogin > 0 || script.Mark&enum.MarkHttp > 0 || script.Mark&enum.MarkTask > 0 {
			// 加载配置表
			cfg.ConfigLoad()
		}
		url := app.GetSettings().Settings["MongodbURL"].(string)
		dbname := app.GetSettings().Settings["MongodbDB"].(string)
		// 初始化db
		db.InitMongoDB(url, dbname)
		redisUrl := app.GetSettings().Settings["RedisURL"].(string)

		password, ok := app.GetSettings().Settings["RedisPassword"].(string)
		if !ok {
			password = ""
		}
		db.InitRedis(redisUrl, password)

		// 初始化数数上报
		ta.Init()

		if com.IsDebug() {
			test()
		}
	})

	// 启动任务服务
	jobrunner.Start()

	// 应用启动完成
	app.OnStartup(func(app module.App) {
		script.AddRPCSerializeOrNot(app)
		ut.Init(time.Now().UnixNano())
		// 启动区服
		if script.Mark&enum.MarkGame > 0 {
			wechatpay.Init()
			// 初始化数据
			InitGlobalMgr()
			// 初始化gm指令
			cmd.InitGm()
			// 启动区服
			manager.GlobalGetAreaManager().Launch(script.Sid, true)
			lua.InitRedisScript()
			// structs.TestRobot(1)
			// 设置pvp赛季重置时间
			nextResetTime := ut.GetNextRefreshTimeOfDoubleWeek(int64(cfg.Misc_CContainer.GetObj().RefreshTime))
			seconds := (nextResetTime - time.Now().UnixMilli()) / 1000
			db.GetRedis().SetNX(context.TODO(), db.RKPvpNormalRankSeason(), "1", time.Duration(seconds)*time.Second)
		}
	})
	// 按需启动模块
	runMods := make([]module.Module, 0)
	// game模块
	if script.Mark&enum.MarkGame > 0 {
		runMods = append(runMods, game.Module())
	}
	// login模块
	if script.Mark&enum.MarkLogin > 0 {
		runMods = append(runMods, login.Module())
	}
	// gate模块
	if script.Mark&enum.MarkGate > 0 {
		runMods = append(runMods, mgate.Module())
	}
	// http模块
	if script.Mark&enum.MarkHttp > 0 {
		runMods = append(runMods, http.Module())
	}
	//task模块
	if script.Mark&enum.MarkTask > 0 {
		runMods = append(runMods, task.Module())
	}

	err = app.Run(runMods...)
	if err != nil {
		log.Error(err.Error())
	}
}

// InitGlobalMgr 有些mgr创建时需要init才能有值，尤其是需要加载数据库数据的
func InitGlobalMgr() {
	manager.GlobalGetCounterManager()
	manager.GlobalGetPlayerManager()
}

// MultipleProcessFix 单机多进程运行fix，主要是调试节点选择，但是这个节点选择器有待优化，节点选择的rand并不靠谱，负载并不均衡
func MultipleProcessFix(app module.App) {
	pid := strconv.Itoa(os.Getpid())
	err := app.Options().Selector.Init(selector.SetStrategy(func(services []*registry.Service) selector.Next {
		var nodes []*registry.Node
		for _, service := range services {
			for _, node := range service.Nodes {
				if node.Metadata["pid"] == pid {
					nodes = append(nodes, node)
				}
			}
		}
		var mtx deadlock.Mutex
		return func() (*registry.Node, error) {
			mtx.Lock()
			defer mtx.Unlock()
			if len(nodes) == 0 {
				return nil, fmt.Errorf("no node")
			}
			index := rand.Intn(len(nodes))
			return nodes[index], nil
		}
	}))
	if err != nil {
		panic(err.Error())
	}
}

func test() {
	// w := &structs.WantedModule{}
	// condAry := []*structs.WantedCondition{
	// 	{Type: wanted_condition_type.ANIMAL_TYPE, Value: 1},
	// 	{Type: wanted_condition_type.ANIMAL_TYPE, Value: 1},
	// 	{Type: wanted_condition_type.ANIMAL_TYPE, Value: 1},
	// }
	// cnt, _ := w.TestGetMinPeople(condAry)
	// fmt.Println(cnt)
}

/**
这里是一些备注

"Log": {
	// 日志远程投递
    "conn": {
      "net": "tcp",
      "reconnect": true,
      "reconnectOnMsg": true,
      "addr": "127.0.0.1:6002"
    },
	// 日志写入本地文件
    "file": {
      "filename": "app.log",
      "daily": true,
      "maxdays": 30,
      "maxsize": 536870912
    }
  }

*/
