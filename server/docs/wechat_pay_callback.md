# 微信支付回调通知接口实现

## 概述

本文档描述了微信支付V3 API回调通知接口的实现，包括配置、使用方法和注意事项。

## 功能特性

- ✅ 支持微信支付V3 API回调通知
- ✅ 自动验证回调签名
- ✅ 自动解密回调数据
- ✅ 支持多种事件类型处理
- ✅ 完整的错误处理和日志记录
- ✅ 订单状态自动更新

## 支持的事件类型

1. **TRANSACTION.SUCCESS** - 支付成功
2. **REFUND.SUCCESS** - 退款成功  
3. **REFUND.ABNORMAL** - 退款异常
4. **REFUND.CLOSED** - 退款关闭

## 配置说明

### 1. 微信支付配置

在 `server/utils/wechat_pay/pay.go` 中配置以下常量：

```go
const (
    WECHAT_MCH_ID           = "你的商户号"                    // 商户号
    WECHAT_CERT_SERIAL_NO   = "你的证书序列号"                // 商户API证书序列号
    WECHAT_PRIVATE_KEY_PATH = "/path/to/apiclient_key.pem"   // 商户私钥文件路径
    WECHAT_PUBLIC_KEY_ID    = "你的公钥ID"                   // 微信支付公钥ID
    WECHAT_PUBLIC_KEY_PATH  = "/path/to/wxp_pub.pem"        // 微信支付公钥文件路径
    WECHAT_APIV3_KEY        = "你的APIv3密钥"                // APIv3密钥，用于回调解密
    WECHAT_APPID            = "你的微信应用ID"                // 微信应用ID
    WECHAT_NOTIFY_URL       = "https://yourdomain.com/notify/wechatNotify" // 回调URL
)
```

### 2. 证书文件准备

确保以下证书文件存在并可读：
- 商户API证书私钥文件 (`apiclient_key.pem`)
- 微信支付公钥文件 (`wxp_pub.pem`)

### 3. 回调URL配置

在微信支付商户平台配置回调URL：
```
https://yourdomain.com/notify/wechatNotify
```

## API接口

### 回调接口

**URL:** `POST /notify/wechatNotify`

**请求格式:** JSON (微信支付标准格式)

**响应格式:**
```json
{
    "code": "SUCCESS",
    "message": "成功"
}
```

## 实现细节

### 1. 签名验证

接口会自动验证微信支付的签名，确保回调的真实性和完整性。

### 2. 数据解密

使用AEAD_AES_256_GCM算法自动解密回调数据。

### 3. 订单处理

- 支付成功时自动更新订单状态为 `PAY`
- 退款成功时自动更新订单状态为 `REFUND`
- 自动触发奖励发放逻辑

### 4. 错误处理

- 签名验证失败返回400错误
- 数据解密失败返回400错误
- 配置未初始化返回500错误
- 详细的错误日志记录

## 使用示例

### 1. 创建订单

```go
// 在PayModule中创建订单
code, options := player.Pay.CreateOrder("product_001", pb.OrderPayPlatform_WechatPay)
if code == 0 {
    // 订单创建成功，options包含prepay_id等信息
    prepayId := options["prepay_id"]
    orderId := options["orderId"]
}
```

### 2. 订单状态查询

```go
// 查找订单
order := player.Pay.FindOrderById(orderId)
if order != nil {
    // 检查订单状态
    switch order.State {
    case pb.OrderState_NOT_PAY:
        // 未支付
    case pb.OrderState_PAY:
        // 已支付未发放
    case pb.OrderState_FINISH:
        // 已完成
    case pb.OrderState_REFUND:
        // 已退款
    }
}
```

## 注意事项

### 1. 安全性

- 所有回调都会进行签名验证
- 使用HTTPS确保传输安全
- APIv3密钥需要妥善保管

### 2. 幂等性

- 微信可能会重复发送回调通知
- 订单状态更新具有幂等性
- 重复处理不会产生副作用

### 3. 超时处理

- 回调处理应在5秒内完成
- 超时的订单会自动标记为 `FAIL_TIMEOUT`

### 4. 日志记录

- 所有回调都有详细的日志记录
- 便于问题排查和监控

## 故障排除

### 1. 签名验证失败

- 检查证书文件是否正确
- 确认公钥ID是否匹配
- 验证时间戳是否在有效范围内

### 2. 解密失败

- 检查APIv3密钥是否正确
- 确认加密算法是否为AEAD_AES_256_GCM

### 3. 订单未找到

- 确认订单ID是否正确
- 检查订单是否已经完成或超时

## 开发建议

1. 在测试环境充分测试回调处理逻辑
2. 监控回调处理的成功率和响应时间
3. 定期检查证书有效期
4. 建立完善的日志监控和告警机制

## 相关文件

- `server/task/http.go` - HTTP回调接口实现
- `server/utils/wechat_pay/pay.go` - 微信支付配置
- `server/utils/wechat_pay/index.go` - 微信支付工具函数
- `server/base/structs/pay.go` - 订单处理逻辑
