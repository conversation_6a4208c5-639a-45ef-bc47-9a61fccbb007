# 微信支付回调接口测试

## 测试环境准备

### 1. 配置微信支付参数

在 `server/utils/wechat_pay/pay.go` 中配置实际的微信支付参数：

```go
const (
    WECHAT_MCH_ID           = "你的实际商户号"
    WECHAT_CERT_SERIAL_NO   = "你的实际证书序列号"
    WECHAT_PRIVATE_KEY_PATH = "bin/conf/apiclient_key.pem"
    WECHAT_PUBLIC_KEY_ID    = "你的实际公钥ID"
    WECHAT_PUBLIC_KEY_PATH  = "bin/conf/wxp_pub.pem"
    WECHAT_APIV3_KEY        = "你的实际APIv3密钥"
    WECHAT_APPID            = "你的实际微信应用ID"
    WECHAT_NOTIFY_URL       = "https://yourdomain.com/notify/wechatNotify"
)
```

### 2. 证书文件

将微信支付证书文件放置到 `server/bin/conf/` 目录：
- `apiclient_key.pem` - 商户私钥
- `wxp_pub.pem` - 微信支付公钥

### 3. 启动服务

```bash
cd server
go run main.go
```

## 测试用例

### 1. 支付成功回调测试

使用curl模拟微信支付成功回调：

```bash
curl -X POST http://localhost:8080/notify/wechatNotify \
  -H "Content-Type: application/json" \
  -H "Wechatpay-Timestamp: $(date +%s)" \
  -H "Wechatpay-Nonce: test_nonce_123456" \
  -H "Wechatpay-Signature: test_signature" \
  -H "Wechatpay-Serial: 你的证书序列号" \
  -d '{
    "id": "test_notification_id",
    "create_time": "2023-12-01T10:00:00+08:00",
    "event_type": "TRANSACTION.SUCCESS",
    "resource_type": "encrypt-resource",
    "resource": {
      "algorithm": "AEAD_AES_256_GCM",
      "ciphertext": "加密的交易数据",
      "associated_data": "transaction",
      "nonce": "test_nonce",
      "original_type": "transaction"
    },
    "summary": "支付成功"
  }'
```

### 2. 退款成功回调测试

```bash
curl -X POST http://localhost:8080/notify/wechatNotify \
  -H "Content-Type: application/json" \
  -H "Wechatpay-Timestamp: $(date +%s)" \
  -H "Wechatpay-Nonce: test_nonce_123456" \
  -H "Wechatpay-Signature: test_signature" \
  -H "Wechatpay-Serial: 你的证书序列号" \
  -d '{
    "id": "test_refund_notification_id",
    "create_time": "2023-12-01T10:00:00+08:00",
    "event_type": "REFUND.SUCCESS",
    "resource_type": "encrypt-resource",
    "resource": {
      "algorithm": "AEAD_AES_256_GCM",
      "ciphertext": "加密的退款数据",
      "associated_data": "refund",
      "nonce": "test_nonce",
      "original_type": "refund"
    },
    "summary": "退款成功"
  }'
```

## 预期结果

### 1. 成功响应

```json
{
    "code": "SUCCESS",
    "message": "成功"
}
```

### 2. 日志输出

```
[INFO] 收到微信支付回调通知
[INFO] 微信支付回调请求体: {...}
[INFO] 微信支付回调通知解析成功, 事件类型: TRANSACTION.SUCCESS
[INFO] 处理微信支付成功通知
[INFO] 微信支付成功: 订单号=xxx, 微信订单号=xxx, 状态=SUCCESS, 支付时间=xxx
[INFO] 更新微信订单状态: 订单号=xxx, 状态=PAY
```

### 3. 错误情况

#### 签名验证失败
```
[ERROR] 解析微信支付回调通知失败: validate notification err: invalid signature
```

#### 解密失败
```
[ERROR] 解析微信支付回调通知失败: notification decrypt err: ...
```

#### 配置未初始化
```
[ERROR] 微信支付配置未初始化
```

## 注意事项

1. **测试环境**: 建议先在测试环境进行充分测试
2. **证书有效性**: 确保证书文件有效且路径正确
3. **网络连通性**: 确保服务器可以接收微信的回调请求
4. **HTTPS**: 生产环境必须使用HTTPS
5. **防火墙**: 确保回调端口对外开放

## 调试技巧

1. **查看日志**: 所有关键步骤都有详细日志
2. **验证配置**: 检查微信支付配置是否正确
3. **测试签名**: 可以先注释掉签名验证进行基础功能测试
4. **检查证书**: 确认证书文件格式和内容正确

## 生产环境部署

1. **域名配置**: 配置正确的回调域名
2. **SSL证书**: 配置有效的SSL证书
3. **负载均衡**: 如有多台服务器，确保回调能正确路由
4. **监控告警**: 设置回调处理的监控和告警
5. **日志收集**: 配置日志收集和分析系统

## 常见问题

### Q: 回调接口返回404
A: 检查路由配置和服务启动状态

### Q: 签名验证失败
A: 检查证书配置和时间同步

### Q: 解密失败
A: 检查APIv3密钥配置

### Q: 订单状态未更新
A: 检查订单查找逻辑和数据库连接

## 相关链接

- [微信支付开发文档](https://pay.weixin.qq.com/doc/v3/merchant/4013070368)
- [微信支付回调通知](https://pay.weixin.qq.com/doc/v3/merchant/4012138956)
- [微信支付签名验证](https://pay.weixin.qq.com/doc/v3/merchant/4012138957)
