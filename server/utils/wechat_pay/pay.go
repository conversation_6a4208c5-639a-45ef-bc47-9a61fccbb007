package wechatpay

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/huyangv/vmqant/log"
)

var config *MchConfig

// 微信支付配置常量
const (
	WECHAT_MCH_ID           = "19xxxxxxxx"                 // 商户号，需要替换为实际值
	WECHAT_CERT_SERIAL_NO   = "1DDE55AD98Exxxxxxxxxx"      // 商户API证书序列号，需要替换为实际值
	WECHAT_PRIVATE_KEY_PATH = "/path/to/apiclient_key.pem" // 商户API证书私钥文件路径，需要替换为实际路径
	WECHAT_PUBLIC_KEY_ID    = "PUB_KEY_ID_xxxxxxxxxxxxx"   // 微信支付公钥ID，需要替换为实际值
	WECHAT_PUBLIC_KEY_PATH  = "/path/to/wxp_pub.pem"       // 微信支付公钥文件路径，需要替换为实际路径
	WECHAT_APIV3_KEY        = "your_apiv3_key_here"        // APIv3密钥，用于回调通知解密，需要替换为实际值
	WECHAT_APPID            = "wxd678efh567hg6787"         // 微信应用ID，需要替换为实际值
	WECHAT_NOTIFY_URL       = ""                           // 回调通知URL，需要设置为实际的回调地址
)

func Init() {
	if config != nil {
		return
	}
	cfg, err := CreateMchConfig(
		WECHAT_MCH_ID,
		WECHAT_CERT_SERIAL_NO,
		WECHAT_PRIVATE_KEY_PATH,
		WECHAT_PUBLIC_KEY_ID,
		WECHAT_PUBLIC_KEY_PATH,
	)
	if err != nil {
		panic(err)
	}
	config = cfg
}

// GetConfig 获取微信支付配置
func GetConfig() *MchConfig {
	return config
}

// GetApiv3Key 获取APIv3密钥
func GetApiv3Key() string {
	return WECHAT_APIV3_KEY
}

// CreateOrder
//
// Parameters:
//   - desc string 商品描述 必填
//   - orderId string 订单号 必填
//   - timeout time.Duration 超时时间 必填
//   - amount int64 金额 必填
func CreateOrder(desc, orderId string, timeout time.Duration, amount int64) string {
	if timeout <= time.Minute {
		timeout = time.Minute
	}
	expireTime := time.Now().Add(timeout)
	request := &CommonPrepayRequest{
		Appid:       String(WECHAT_APPID),
		Mchid:       String(WECHAT_MCH_ID),
		Description: String(desc),
		OutTradeNo:  String(orderId),
		TimeExpire:  Time(expireTime),
		Attach:      String("暂无自定义数据说明"),
		NotifyUrl:   String(WECHAT_NOTIFY_URL),
		Amount: &CommonAmountInfo{
			Total:    Int64(amount),
			Currency: String("CNY"),
		},
		Detail:     nil,
		SceneInfo:  nil,
		SettleInfo: nil,
	}

	response, err := AppPrepay(config, request)
	if err != nil {
		log.Error("微信支付请求失败: %+v", err)
		return ""
	}

	return *response.PrepayId
}

func QueryOrder(order string, amount int64) (bool, int) {
	request := &QueryByOutTradeNoRequest{
		Mchid:      String(WECHAT_MCH_ID),
		OutTradeNo: String(order),
	}

	response, err := QueryByOutTradeNo(config, request)
	if err != nil {
		log.Error("微信支付请求失败: %+v", err)
		return false, 0
	}
	log.Debug("微信支付查询结果: %+v", response)
	if *response.TradeState != "SUCCESS" {
		return false, 0
	}

	if response.Amount != nil {
		if *response.Amount.PayerTotal != amount {
			log.Error("微信支付金额不一致: %+v", response.Amount)
			return false, 0
		}
	}
	t, _ := time.Parse("2006-01-02 15:04:05", *response.SuccessTime)
	return true, int(t.Unix() / 1e3)
}

func AppPrepay(config *MchConfig, request *CommonPrepayRequest) (response *DirectApiv3AppPrepayResponse, err error) {
	const (
		host   = "https://api.mch.weixin.qq.com"
		method = "POST"
		path   = "/v3/pay/transactions/app"
	)
	reqUrl, err := url.Parse(fmt.Sprintf("%s%s", host, path))
	if err != nil {
		return nil, err
	}
	reqBody, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}
	httpRequest, err := http.NewRequest(method, reqUrl.String(), bytes.NewReader(reqBody))
	if err != nil {
		return nil, err
	}
	httpRequest.Header.Set("Accept", "application/json")
	httpRequest.Header.Set("Wechatpay-Serial", config.WechatPayPublicKeyId())
	httpRequest.Header.Set("Content-Type", "application/json")
	authorization, err := BuildAuthorization(config.MchId(), config.CertificateSerialNo(), config.PrivateKey(), method, reqUrl.RequestURI(), reqBody)
	if err != nil {
		return nil, err
	}
	httpRequest.Header.Set("Authorization", authorization)

	client := &http.Client{}
	httpResponse, err := client.Do(httpRequest)
	if err != nil {
		return nil, err
	}
	respBody, err := ExtractResponseBody(httpResponse)
	if err != nil {
		return nil, err
	}
	if httpResponse.StatusCode >= 200 && httpResponse.StatusCode < 300 {
		// 2XX 成功，验证应答签名
		err = ValidateResponse(
			config.WechatPayPublicKeyId(),
			config.WechatPayPublicKey(),
			&httpResponse.Header,
			respBody,
		)
		if err != nil {
			return nil, err
		}
		response := &DirectApiv3AppPrepayResponse{}
		if err := json.Unmarshal(respBody, response); err != nil {
			return nil, err
		}

		return response, nil
	} else {
		return nil, NewApiException(
			httpResponse.StatusCode,
			httpResponse.Header,
			respBody,
		)
	}
}

func QueryByOutTradeNo(config *MchConfig, request *QueryByOutTradeNoRequest) (response *DirectApiv3QueryResponse, err error) {
	const (
		host   = "https://api.mch.weixin.qq.com"
		method = "GET"
		path   = "/v3/pay/transactions/out-trade-no/{out_trade_no}"
	)

	reqUrl, err := url.Parse(fmt.Sprintf("%s%s", host, path))
	if err != nil {
		return nil, err
	}
	reqUrl.Path = strings.Replace(reqUrl.Path, "{out_trade_no}", url.PathEscape(*request.OutTradeNo), -1)
	query := reqUrl.Query()
	query.Add("mchid", *request.Mchid)
	reqUrl.RawQuery = query.Encode()
	httpRequest, err := http.NewRequest(method, reqUrl.String(), nil)
	if err != nil {
		return nil, err
	}
	httpRequest.Header.Set("Accept", "application/json")
	httpRequest.Header.Set("Wechatpay-Serial", config.WechatPayPublicKeyId())
	authorization, err := BuildAuthorization(config.MchId(), config.CertificateSerialNo(), config.PrivateKey(), method, reqUrl.RequestURI(), nil)
	if err != nil {
		return nil, err
	}
	httpRequest.Header.Set("Authorization", authorization)

	client := &http.Client{}
	httpResponse, err := client.Do(httpRequest)
	if err != nil {
		return nil, err
	}

	respBody, err := ExtractResponseBody(httpResponse)
	if err != nil {
		return nil, err
	}

	if httpResponse.StatusCode >= 200 && httpResponse.StatusCode < 300 {
		// 2XX 成功，验证应答签名
		err = ValidateResponse(
			config.WechatPayPublicKeyId(),
			config.WechatPayPublicKey(),
			&httpResponse.Header,
			respBody,
		)
		if err != nil {
			return nil, err
		}

		if err := json.Unmarshal(respBody, response); err != nil {
			return nil, err
		}

		return response, nil
	} else {
		return nil, NewApiException(
			httpResponse.StatusCode,
			httpResponse.Header,
			respBody,
		)
	}
}

type CommonPrepayRequest struct {
	Appid         *string           `json:"appid,omitempty"`
	Mchid         *string           `json:"mchid,omitempty"`
	Description   *string           `json:"description,omitempty"`
	OutTradeNo    *string           `json:"out_trade_no,omitempty"`
	TimeExpire    *time.Time        `json:"time_expire,omitempty"`
	Attach        *string           `json:"attach,omitempty"`
	NotifyUrl     *string           `json:"notify_url,omitempty"`
	GoodsTag      *string           `json:"goods_tag,omitempty"`
	SupportFapiao *bool             `json:"support_fapiao,omitempty"`
	Amount        *CommonAmountInfo `json:"amount,omitempty"`
	Detail        *CouponInfo       `json:"detail,omitempty"`
	SceneInfo     *CommonSceneInfo  `json:"scene_info,omitempty"`
	SettleInfo    *SettleInfo       `json:"settle_info,omitempty"`
}

type DirectApiv3AppPrepayResponse struct {
	PrepayId *string `json:"prepay_id,omitempty"`
}

type CommonAmountInfo struct {
	Total    *int64  `json:"total,omitempty"`
	Currency *string `json:"currency,omitempty"`
}

type CouponInfo struct {
	CostPrice   *int64        `json:"cost_price,omitempty"`
	InvoiceId   *string       `json:"invoice_id,omitempty"`
	GoodsDetail []GoodsDetail `json:"goods_detail,omitempty"`
}

type CommonSceneInfo struct {
	PayerClientIp *string    `json:"payer_client_ip,omitempty"`
	DeviceId      *string    `json:"device_id,omitempty"`
	StoreInfo     *StoreInfo `json:"store_info,omitempty"`
}

type SettleInfo struct {
	ProfitSharing *bool `json:"profit_sharing,omitempty"`
}

type GoodsDetail struct {
	MerchantGoodsId  *string `json:"merchant_goods_id,omitempty"`
	WechatpayGoodsId *string `json:"wechatpay_goods_id,omitempty"`
	GoodsName        *string `json:"goods_name,omitempty"`
	Quantity         *int64  `json:"quantity,omitempty"`
	UnitPrice        *int64  `json:"unit_price,omitempty"`
}

type StoreInfo struct {
	Id       *string `json:"id,omitempty"`
	Name     *string `json:"name,omitempty"`
	AreaCode *string `json:"area_code,omitempty"`
	Address  *string `json:"address,omitempty"`
}

type CommRespAmountInfo struct {
	Total         *int64  `json:"total,omitempty"`
	PayerTotal    *int64  `json:"payer_total,omitempty"`
	Currency      *string `json:"currency,omitempty"`
	PayerCurrency *string `json:"payer_currency,omitempty"`
}

type CommRespSceneInfo struct {
	DeviceId *string `json:"device_id,omitempty"`
}

type CommRespPayerInfo struct {
	Openid *string `json:"openid,omitempty"`
}

type QueryByOutTradeNoRequest struct {
	Mchid      *string `json:"mchid,omitempty"`
	OutTradeNo *string `json:"out_trade_no,omitempty"`
}

func (o *QueryByOutTradeNoRequest) MarshalJSON() ([]byte, error) {
	type Alias QueryByOutTradeNoRequest
	a := &struct {
		Mchid      *string `json:"mchid,omitempty"`
		OutTradeNo *string `json:"out_trade_no,omitempty"`
		*Alias
	}{
		// 序列化时移除非 Body 字段
		Mchid:      nil,
		OutTradeNo: nil,
		Alias:      (*Alias)(o),
	}
	return json.Marshal(a)
}

type GoodsDetailInPromotion struct {
	GoodsId        *string `json:"goods_id,omitempty"`
	Quantity       *int64  `json:"quantity,omitempty"`
	UnitPrice      *int64  `json:"unit_price,omitempty"`
	DiscountAmount *int64  `json:"discount_amount,omitempty"`
	GoodsRemark    *string `json:"goods_remark,omitempty"`
}

type DirectApiv3QueryResponse struct {
	Appid           *string             `json:"appid,omitempty"`
	Mchid           *string             `json:"mchid,omitempty"`
	OutTradeNo      *string             `json:"out_trade_no,omitempty"`
	TransactionId   *string             `json:"transaction_id,omitempty"`
	TradeType       *string             `json:"trade_type,omitempty"`
	TradeState      *string             `json:"trade_state,omitempty"`
	TradeStateDesc  *string             `json:"trade_state_desc,omitempty"`
	BankType        *string             `json:"bank_type,omitempty"`
	Attach          *string             `json:"attach,omitempty"`
	SuccessTime     *string             `json:"success_time,omitempty"`
	Payer           *CommRespPayerInfo  `json:"payer,omitempty"`
	Amount          *CommRespAmountInfo `json:"amount,omitempty"`
	SceneInfo       *CommRespSceneInfo  `json:"scene_info,omitempty"`
	PromotionDetail []PromotionDetail   `json:"promotion_detail,omitempty"`
}

type PromotionDetail struct {
	CouponId            *string                  `json:"coupon_id,omitempty"`
	Name                *string                  `json:"name,omitempty"`
	Scope               *string                  `json:"scope,omitempty"`
	Type                *string                  `json:"type,omitempty"`
	Amount              *int64                   `json:"amount,omitempty"`
	StockId             *string                  `json:"stock_id,omitempty"`
	WechatpayContribute *int64                   `json:"wechatpay_contribute,omitempty"`
	MerchantContribute  *int64                   `json:"merchant_contribute,omitempty"`
	OtherContribute     *int64                   `json:"other_contribute,omitempty"`
	Currency            *string                  `json:"currency,omitempty"`
	GoodsDetail         []GoodsDetailInPromotion `json:"goods_detail,omitempty"`
}
